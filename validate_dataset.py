#!/usr/bin/env python3
"""
数据集验证脚本
用于检查自定义数据集的格式和完整性
"""

import os
import numpy as np
from collections import defaultdict

def validate_dataset_structure(dataset_path):
    """
    验证数据集文件夹结构
    """
    print("=== 验证数据集结构 ===")
    
    if not os.path.exists(dataset_path):
        print(f"❌ 数据集路径不存在: {dataset_path}")
        return False
    
    folders = []
    for item in os.listdir(dataset_path):
        item_path = os.path.join(dataset_path, item)
        if os.path.isdir(item_path):
            folders.append(item)
    
    if len(folders) == 0:
        print(f"❌ 数据集路径下没有找到子文件夹: {dataset_path}")
        return False
    
    print(f"✅ 发现 {len(folders)} 个类别文件夹:")
    folders.sort()
    
    file_stats = defaultdict(list)
    total_files = 0
    
    for i, folder in enumerate(folders):
        folder_path = os.path.join(dataset_path, folder)
        files = [f for f in os.listdir(folder_path) if f.endswith(('.pcap', '.npz'))]
        file_count = len(files)
        total_files += file_count
        
        print(f"  {i:2d}: {folder:<20} ({file_count} 文件)")
        
        # 统计文件类型
        for file in files:
            if file.endswith('.pcap'):
                file_stats['pcap'].append((folder, file))
            elif file.endswith('.npz'):
                file_stats['npz'].append((folder, file))
    
    print(f"\n总计: {total_files} 个数据文件")
    print(f"  - .pcap 文件: {len(file_stats['pcap'])}")
    print(f"  - .npz 文件: {len(file_stats['npz'])}")
    
    return True, folders, file_stats

def validate_npz_files(file_stats):
    """
    验证npz文件格式
    """
    print("\n=== 验证NPZ文件格式 ===")
    
    if len(file_stats['npz']) == 0:
        print("⚠️  没有找到.npz文件，需要先运行 pcap2npy.py 进行转换")
        return True
    
    # 随机检查几个npz文件
    sample_files = file_stats['npz'][:min(5, len(file_stats['npz']))]
    
    for folder, filename in sample_files:
        file_path = os.path.join("/home/<USER>/NetMamba/dataset/database_clean", folder, filename)
        
        try:
            data = np.load(file_path, allow_pickle=True)
            keys = list(data.keys())
            
            print(f"✅ {folder}/{filename}")
            print(f"   包含字段: {keys}")
            
            # 检查必要字段
            required_fields = ['header', 'payload']
            missing_fields = [field for field in required_fields if field not in keys]
            
            if missing_fields:
                print(f"   ⚠️  缺少字段: {missing_fields}")
            
            # 检查数据形状
            if 'payload' in keys:
                payload_shape = data['payload'].shape
                print(f"   payload形状: {payload_shape}")
            
            if 'header' in keys:
                header_shape = data['header'].shape
                print(f"   header形状: {header_shape}")
                
        except Exception as e:
            print(f"❌ {folder}/{filename}: 读取失败 - {str(e)}")
            return False
    
    return True

def check_processed_data():
    """
    检查预处理后的数据文件
    """
    print("\n=== 检查预处理数据 ===")
    
    processed_path = "/home/<USER>/NetMamba/dataset/processed"
    required_files = [
        'train.npz', 'test.npz',
        'header_train.npz', 'header_test.npz',
        'train_graph.dgl', 'test_graph.dgl',
        'header_train_graph.dgl', 'header_test_graph.dgl'
    ]
    
    if not os.path.exists(processed_path):
        print(f"⚠️  预处理数据目录不存在: {processed_path}")
        print("   需要运行: python preprocess.py --dataset custom")
        return False
    
    missing_files = []
    existing_files = []
    
    for file in required_files:
        file_path = os.path.join(processed_path, file)
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            existing_files.append((file, file_size))
            print(f"✅ {file} ({file_size / 1024 / 1024:.1f} MB)")
        else:
            missing_files.append(file)
            print(f"❌ {file} (缺失)")
    
    if missing_files:
        print(f"\n⚠️  缺少 {len(missing_files)} 个预处理文件")
        print("   需要运行: python preprocess.py --dataset custom")
        return False
    else:
        print(f"\n✅ 所有预处理文件都存在")
        return True

def main():
    dataset_path = "/home/<USER>/NetMamba/dataset/database_clean"
    
    print("=== 数据集验证工具 ===")
    print(f"数据集路径: {dataset_path}")
    
    # 验证数据集结构
    result = validate_dataset_structure(dataset_path)
    if not result:
        return
    
    success, folders, file_stats = result
    
    # 验证npz文件格式
    if not validate_npz_files(file_stats):
        return
    
    # 检查预处理数据
    processed_ready = check_processed_data()
    
    # 总结
    print("\n=== 验证总结 ===")
    print(f"✅ 数据集结构正确 ({len(folders)} 个类别)")
    
    if len(file_stats['pcap']) > 0 and len(file_stats['npz']) == 0:
        print("📋 下一步: 运行 python pcap2npy.py --dataset custom")
    elif len(file_stats['npz']) > 0 and not processed_ready:
        print("📋 下一步: 运行 python preprocess.py --dataset custom")
    elif processed_ready:
        print("📋 下一步: 运行 python train_new.py --dataset custom --point 15")
    
    print("\n数据集验证完成!")

if __name__ == "__main__":
    main()
