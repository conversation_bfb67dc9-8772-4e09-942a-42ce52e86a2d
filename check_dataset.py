#!/usr/bin/env python3
"""
简单的数据集检查脚本
用于检查服务器上的数据集结构
"""

import os

def check_dataset():
    dataset_path = "/home/<USER>/NetMamba/dataset/database_clean"
    
    print("=== 数据集结构检查 ===")
    print(f"数据集路径: {dataset_path}")
    
    if not os.path.exists(dataset_path):
        print(f"❌ 数据集路径不存在: {dataset_path}")
        return
    
    folders = []
    for item in os.listdir(dataset_path):
        item_path = os.path.join(dataset_path, item)
        if os.path.isdir(item_path):
            folders.append(item)
    
    folders.sort()
    print(f"\n发现 {len(folders)} 个类别文件夹:")
    
    total_files = 0
    for i, folder in enumerate(folders):
        folder_path = os.path.join(dataset_path, folder)
        files = [f for f in os.listdir(folder_path) if f.endswith(('.pcap', '.npz'))]
        file_count = len(files)
        total_files += file_count
        
        print(f"  {i:2d}: {folder:<20} ({file_count} 文件)")
        
        # 显示前几个文件名作为示例
        if files:
            sample_files = files[:3]  # 显示前3个文件
            for file in sample_files:
                print(f"      - {file}")
            if len(files) > 3:
                print(f"      ... 还有 {len(files) - 3} 个文件")
    
    print(f"\n总计: {total_files} 个数据文件")
    
    # 生成配置代码
    print("\n=== 生成的配置代码 ===")
    print("将以下代码复制到 config.py 的 CustomDatasetConfig 类中:")
    print()
    print(f"    NUM_CLASSES = {len(folders)}")
    print("    DIR_PATH_DICT = {", end="")
    for i, folder in enumerate(folders):
        if i == 0:
            print(f"{i}: r'/home/<USER>/NetMamba/dataset/database_clean/{folder}',")
        else:
            print(f"                     {i}: r'/home/<USER>/NetMamba/dataset/database_clean/{folder}',")
    print("                     }")

if __name__ == "__main__":
    check_dataset()
