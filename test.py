# -*- coding: utf-8 -*-
import numpy as np
from imblearn.over_sampling import SMOTE
from imblearn.over_sampling import RandomOverSampler
from imblearn.over_sampling import ADASYN
from imblearn.under_sampling import RandomUnderSampler
from imblearn.under_sampling import NearMiss
from sklearn.datasets import make_classification

# 随机生成原始数据
x, y = make_classification(n_samples=1000, n_features=5, n_classes=2,
                           weights=[0.1, 0.9], random_state=123)
print('原始正样本数：', np.sum(y == 1), '原始负样本数：', np.sum(y == 0),   '原始总数：', len(x))


# smote过采样
smote = SMOTE()
x_new, y_new = smote.fit_resample(x, y)
print('smote后正样本数：', np.sum(y_new == 1), 'smote后负样本数：', np.sum(y_new == 0), 'smote后总数：', len(x_new))

# 随机欠采样
rus = RandomUnderSampler()
x_new2, y_new2 = rus.fit_resample(x, y)
print('随机欠采样后正样本数：', np.sum(y_new2 == 1), '随机欠采样后负样本数：', np.sum(y_new2 == 0), '随机欠采样后总数：', len(x_new2))


