# 自定义数据集使用指南

本指南将帮助您将自己的网络流量数据集适配到CLE-TFE项目中。

## 前提条件

您的数据集应该满足以下条件：
- 数据集路径: `/home/<USER>/NetMamba/dataset/database_clean`
- 包含13个子文件夹，每个文件夹代表一种流量类型
- 每个子文件夹中包含网络流量数据文件（.pcap 或 .npz 格式）

## 步骤1: 自动配置数据集

运行自动配置脚本来检测您的数据集结构：

```bash
python setup_custom_dataset.py
```

这个脚本会：
1. 自动检测 `/home/<USER>/NetMamba/dataset/database_clean` 下的所有子文件夹
2. 显示发现的文件夹列表
3. 更新 `config.py` 中的 `CustomDatasetConfig` 类
4. 设置正确的类别数量和文件夹路径映射

## 步骤2: 数据格式转换（如果需要）

如果您的数据是 .pcap 格式，需要先转换为 .npz 格式：

```bash
python pcap2npy.py --dataset custom
```

这会将每个类别文件夹中的 .pcap 文件转换为 .npz 格式。

## 步骤3: 数据预处理

运行预处理脚本来构建图结构数据：

```bash
python preprocess.py --dataset custom
```

这个步骤会：
1. 从每个类别的 .npz 文件中提取流量数据
2. 将数据分割为训练集和测试集
3. 构建字节级流量图
4. 生成以下文件：
   - `/home/<USER>/NetMamba/dataset/processed/train.npz`
   - `/home/<USER>/NetMamba/dataset/processed/test.npz`
   - `/home/<USER>/NetMamba/dataset/processed/train_graph.dgl`
   - `/home/<USER>/NetMamba/dataset/processed/test_graph.dgl`
   - 以及对应的header文件

## 步骤4: 模型训练

开始训练模型：

```bash
python train_new.py --dataset custom --point 15
```

主要参数说明：
- `--dataset custom`: 使用自定义数据集配置
- `--point 15`: 每个流量序列使用15个数据包
- `--coe 0.1`: 对比学习损失权重
- `--coe_graph 0.1`: 图对比学习损失权重

## 步骤5: 模型测试

训练完成后，测试模型性能：

```bash
python test_new.py --dataset custom --prefix mix_model_custom --point 15
```

## 配置文件说明

在 `config.py` 中，`CustomDatasetConfig` 类包含以下重要配置：

```python
class CustomDatasetConfig(Config):
    # 类别数量（自动设置为您的文件夹数量）
    NUM_CLASSES = 13
    
    # 文件夹路径映射（自动生成）
    DIR_PATH_DICT = {
        0: r'/home/<USER>/NetMamba/dataset/database_clean/folder_0',
        1: r'/home/<USER>/NetMamba/dataset/database_clean/folder_1',
        # ... 更多文件夹
    }
    
    # 训练参数（可根据需要调整）
    BATCH_SIZE = 32
    MAX_EPOCH = 100
    LR = 1e-2
```

## 常见问题

### Q1: 数据预处理时出现内存错误
**解决方案**: 减少 `MAX_SEG_PER_CLASS` 的值，或者增加系统内存。

### Q2: 训练时GPU内存不足
**解决方案**: 
- 减少 `BATCH_SIZE`
- 减少 `point` 参数值
- 使用梯度累积：设置 `GRADIENT_ACCUMULATION > 1`

### Q3: 模型收敛慢或效果不好
**解决方案**:
- 调整学习率 `LR`
- 增加训练轮数 `MAX_EPOCH`
- 调整dropout参数
- 尝试不同的对比学习权重

### Q4: 数据集不平衡
**解决方案**: 在配置中调整 `MAX_SEG_PER_CLASS` 来限制每个类别的最大样本数。

## 文件结构

完成所有步骤后，您的文件结构应该如下：

```
/home/<USER>/NetMamba/dataset/
├── database_clean/          # 原始数据集
│   ├── category_0/         # 类别0的数据文件
│   ├── category_1/         # 类别1的数据文件
│   └── ...
└── processed/              # 处理后的数据
    ├── train.npz
    ├── test.npz
    ├── train_graph.dgl
    ├── test_graph.dgl
    └── ...
```

## 性能优化建议

1. **数据预处理优化**:
   - 使用SSD存储来加速I/O
   - 适当调整 `NUM_WORKERS` 参数

2. **训练优化**:
   - 使用GPU训练
   - 启用混合精度训练（如果支持）
   - 使用数据并行（多GPU）

3. **内存优化**:
   - 减少批次大小
   - 使用梯度检查点
   - 定期清理GPU缓存

## 支持

如果遇到问题，请检查：
1. 数据格式是否正确
2. 路径配置是否正确
3. 依赖包是否完整安装
4. 系统资源是否充足
