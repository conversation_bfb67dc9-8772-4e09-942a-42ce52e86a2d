#!/usr/bin/env python3
"""
自定义数据集完整运行脚本
自动化执行从数据预处理到模型训练的完整流程
"""

import os
import sys
import subprocess
import time

def run_command(command, description):
    """
    运行命令并显示进度
    """
    print(f"\n{'='*50}")
    print(f"🚀 {description}")
    print(f"命令: {command}")
    print(f"{'='*50}")
    
    start_time = time.time()
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ {description} 完成 (耗时: {duration:.1f}秒)")
        
        if result.stdout:
            print("输出:")
            print(result.stdout)
            
        return True
        
    except subprocess.CalledProcessError as e:
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"❌ {description} 失败 (耗时: {duration:.1f}秒)")
        print(f"错误码: {e.returncode}")
        
        if e.stdout:
            print("标准输出:")
            print(e.stdout)
            
        if e.stderr:
            print("错误输出:")
            print(e.stderr)
            
        return False

def check_prerequisites():
    """
    检查前提条件
    """
    print("=== 检查前提条件 ===")
    
    # 检查数据集路径
    dataset_path = "/home/<USER>/NetMamba/dataset/database_clean"
    if not os.path.exists(dataset_path):
        print(f"❌ 数据集路径不存在: {dataset_path}")
        return False
    
    # 检查是否有子文件夹
    folders = [d for d in os.listdir(dataset_path) 
              if os.path.isdir(os.path.join(dataset_path, d))]
    
    if len(folders) == 0:
        print(f"❌ 数据集路径下没有子文件夹: {dataset_path}")
        return False
    
    print(f"✅ 发现 {len(folders)} 个类别文件夹")
    
    # 检查必要的脚本文件
    required_scripts = [
        'setup_custom_dataset.py',
        'pcap2npy.py', 
        'preprocess.py',
        'train_new.py'
    ]
    
    for script in required_scripts:
        if not os.path.exists(script):
            print(f"❌ 缺少必要脚本: {script}")
            return False
    
    print("✅ 所有必要脚本都存在")
    
    # 创建输出目录
    processed_dir = "/home/<USER>/NetMamba/dataset/processed"
    if not os.path.exists(processed_dir):
        os.makedirs(processed_dir)
        print(f"✅ 创建输出目录: {processed_dir}")
    
    checkpoints_dir = "./checkpoints"
    if not os.path.exists(checkpoints_dir):
        os.makedirs(checkpoints_dir)
        print(f"✅ 创建模型保存目录: {checkpoints_dir}")
    
    return True

def main():
    print("=== 自定义数据集完整运行脚本 ===")
    print("此脚本将自动执行以下步骤:")
    print("1. 检查前提条件")
    print("2. 配置数据集")
    print("3. 数据格式转换 (如果需要)")
    print("4. 数据预处理")
    print("5. 模型训练")
    print("6. 模型测试")
    
    # 用户确认
    response = input("\n是否继续? (y/n): ")
    if response.lower() != 'y':
        print("操作已取消")
        return
    
    # 检查前提条件
    if not check_prerequisites():
        print("❌ 前提条件检查失败，请解决问题后重试")
        return
    
    # 步骤1: 配置数据集
    if not run_command("python setup_custom_dataset.py", "配置数据集"):
        print("❌ 数据集配置失败")
        return
    
    # 步骤2: 验证数据集
    if not run_command("python validate_dataset.py", "验证数据集"):
        print("⚠️  数据集验证有警告，但继续执行")
    
    # 步骤3: 数据格式转换 (检查是否需要)
    dataset_path = "/home/<USER>/NetMamba/dataset/database_clean"
    folders = [d for d in os.listdir(dataset_path) 
              if os.path.isdir(os.path.join(dataset_path, d))]
    
    need_conversion = False
    for folder in folders:
        folder_path = os.path.join(dataset_path, folder)
        pcap_files = [f for f in os.listdir(folder_path) if f.endswith('.pcap')]
        if pcap_files:
            need_conversion = True
            break
    
    if need_conversion:
        if not run_command("python pcap2npy.py --dataset custom", "PCAP文件转换"):
            print("❌ PCAP文件转换失败")
            return
    else:
        print("⏭️  跳过PCAP转换 (没有发现.pcap文件)")
    
    # 步骤4: 数据预处理
    if not run_command("python preprocess.py --dataset custom", "数据预处理"):
        print("❌ 数据预处理失败")
        return
    
    # 步骤5: 模型训练
    train_command = "python train_new.py --dataset custom --point 15 --coe 0.1 --coe_graph 0.1"
    if not run_command(train_command, "模型训练"):
        print("❌ 模型训练失败")
        return
    
    # 步骤6: 模型测试
    test_command = "python test_new.py --dataset custom --prefix mix_model_custom --point 15"
    if not run_command(test_command, "模型测试"):
        print("⚠️  模型测试失败，但训练已完成")
    
    # 完成
    print("\n" + "="*60)
    print("🎉 自定义数据集训练流程完成!")
    print("="*60)
    print("训练好的模型保存在: ./checkpoints/mix_model_custom.pth")
    print("预处理数据保存在: /home/<USER>/NetMamba/dataset/processed/")
    
    print("\n如需重新训练，可以直接运行:")
    print("python train_new.py --dataset custom --point 15")
    
    print("\n如需测试模型，可以运行:")
    print("python test_new.py --dataset custom --prefix mix_model_custom --point 15")

if __name__ == "__main__":
    main()
