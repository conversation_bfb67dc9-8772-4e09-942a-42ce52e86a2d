#!/usr/bin/env python3
"""
修复版本的pcap2npy.py
专门处理各种pcap文件格式问题
"""

import argparse
import numpy as np
from scapy.all import *
import os
import sys

from utils import show_time, get_bytes_from_raw
from config import *

# 全局变量
PKT_COUNT = 0
p_header_list = []
p_payload_list = []
payload_length = []
pkt_length = []
src_ip = []
dst_ip = []
src_port = []
dst_port = []
time = []
protocol = []
flag = []
mss = []

def safe_get_attr(obj, attr, default=None):
    """安全地获取对象属性"""
    try:
        return getattr(obj, attr, default)
    except:
        return default

def process_packet(pkt):
    """处理单个数据包"""
    global PKT_COUNT, p_header_list, p_payload_list, payload_length, pkt_length
    global src_ip, dst_ip, src_port, dst_port, time, protocol, flag, mss
    
    # 只处理IP数据包
    if not pkt.haslayer("IP"):
        return
    
    # 只处理TCP数据包
    if not pkt.haslayer("TCP"):
        return
    
    try:
        PKT_COUNT += 1
        
        # 获取数据包字节
        try:
            _, p_packet = get_bytes_from_raw(hexdump(pkt, dump=True))
        except:
            print(f"警告: 无法解析数据包 {PKT_COUNT}")
            return
        
        # 获取载荷
        p_payload = []
        if pkt.haslayer("Raw"):
            try:
                _, p_payload = get_bytes_from_raw(hexdump(pkt["Raw"].load, dump=True))
            except:
                p_payload = []
        
        # 计算头部
        p_header = p_packet[:(len(p_packet) - len(p_payload))]
        p_header_list.append(p_header)
        p_payload_list.append(p_payload)
        
        # 长度信息
        payload_length.append(len(p_payload))
        pkt_length.append(len(p_header) + len(p_payload))
        
        # IP地址
        ip_layer = pkt["IP"]
        src_ip.append(safe_get_attr(ip_layer, 'src', '0.0.0.0'))
        dst_ip.append(safe_get_attr(ip_layer, 'dst', '0.0.0.0'))
        
        # 端口信息
        tcp_layer = pkt["TCP"]
        src_port.append(safe_get_attr(tcp_layer, 'sport', 0))
        dst_port.append(safe_get_attr(tcp_layer, 'dport', 0))
        
        # 时间戳
        time.append(safe_get_attr(pkt, 'time', 0.0))
        
        # 协议
        protocol.append(safe_get_attr(ip_layer, 'proto', 6))  # 6 = TCP
        
        # TCP标志
        flag.append(safe_get_attr(tcp_layer, 'flags', 0))
        
        # MSS选项
        mss_value = 0
        try:
            if hasattr(tcp_layer, 'options') and tcp_layer.options:
                for option in tcp_layer.options:
                    if isinstance(option, tuple) and len(option) >= 2:
                        if option[0] == 'MSS':
                            mss_value = option[1]
                            break
        except:
            pass
        mss.append(mss_value)
        
        # 每处理1000个包显示进度
        if PKT_COUNT % 1000 == 0:
            print(f"已处理 {PKT_COUNT} 个数据包...")
            
    except Exception as e:
        print(f"处理数据包时出错: {e}")
        return

def reset_global_vars():
    """重置全局变量"""
    global PKT_COUNT, p_header_list, p_payload_list, payload_length, pkt_length
    global src_ip, dst_ip, src_port, dst_port, time, protocol, flag, mss
    
    PKT_COUNT = 0
    p_header_list.clear()
    p_payload_list.clear()
    payload_length.clear()
    pkt_length.clear()
    src_ip.clear()
    dst_ip.clear()
    src_port.clear()
    dst_port.clear()
    time.clear()
    protocol.clear()
    flag.clear()
    mss.clear()

def pcap2npy_custom(dir_path_dict, save_path_dict):
    """转换pcap文件为npz格式"""
    for category in dir_path_dict:
        dir_path = dir_path_dict[category]
        
        if not os.path.exists(dir_path):
            print(f"警告: 目录不存在 {dir_path}")
            continue
            
        file_list = os.listdir(dir_path)
        pcap_files = [f for f in file_list if f.endswith('.pcap')]
        
        if not pcap_files:
            print(f"警告: 目录 {dir_path} 中没有找到.pcap文件")
            continue
            
        print(f"处理类别 {category}: {os.path.basename(dir_path)} ({len(pcap_files)} 个文件)")
        
        for file in pcap_files:
            reset_global_vars()
            
            file_path = os.path.join(dir_path, file)
            print(f'{show_time()} {file_path} 开始处理')
            
            try:
                # 使用scapy读取pcap文件
                packets = rdpcap(file_path)
                print(f"读取到 {len(packets)} 个数据包")
                
                # 处理每个数据包
                for pkt in packets:
                    process_packet(pkt)
                
                print(f"成功处理 {PKT_COUNT} 个TCP数据包")
                
                # 检查是否有有效数据
                if PKT_COUNT == 0:
                    print(f"警告: 文件 {file} 中没有有效的TCP数据包")
                    continue
                
                # 验证数据一致性
                if not (len(p_header_list) == PKT_COUNT and len(p_payload_list) == PKT_COUNT):
                    print(f"错误: 数据不一致 - headers: {len(p_header_list)}, payloads: {len(p_payload_list)}, count: {PKT_COUNT}")
                    continue
                
                # 保存为npz文件
                save_file = file[:-5] + '.npz'  # 替换.pcap为.npz
                save_path = os.path.join(save_path_dict[category], save_file)
                
                np.savez_compressed(save_path,
                                    header=np.array(p_header_list, dtype=object),
                                    payload=np.array(p_payload_list, dtype=object),
                                    payload_length=np.array(payload_length, dtype=object),
                                    pkt_length=np.array(pkt_length, dtype=object),
                                    src_ip=np.array(src_ip, dtype=object),
                                    dst_ip=np.array(dst_ip, dtype=object),
                                    src_port=np.array(src_port, dtype=object),
                                    dst_port=np.array(dst_port, dtype=object),
                                    time=np.array(time, dtype=object),
                                    protocol=np.array(protocol, dtype=object),
                                    flag=np.array(flag, dtype=object),
                                    mss=np.array(mss, dtype=object))
                
                print(f'✅ 保存完成: {save_path}')
                
            except Exception as e:
                print(f"❌ 处理文件 {file_path} 时出错: {e}")
                continue

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument("--dataset", type=str, help="dataset", required=True)
    opt = parser.parse_args()
    
    if opt.dataset == 'custom':
        config = CustomDatasetConfig()
    elif opt.dataset == 'iscx-vpn':
        config = ISCXVPNConfig()
    elif opt.dataset == 'iscx-nonvpn':
        config = ISCXNonVPNConfig()
    elif opt.dataset == 'iscx-tor':
        config = ISCXTorConfig()
    elif opt.dataset == 'iscx-nontor':
        config = ISCXNonTorConfig()
    else:
        raise Exception('Dataset Error')
    
    print("=== PCAP转NPZ工具 (修复版) ===")
    print(f"数据集类型: {opt.dataset}")
    
    # 获取目录字典
    if hasattr(config, 'DIR_PATH_DICT'):
        if callable(config.DIR_PATH_DICT):
            dir_dict = config.DIR_PATH_DICT()
        else:
            dir_dict = config.DIR_PATH_DICT
    else:
        print("错误: 配置中没有找到DIR_PATH_DICT")
        sys.exit(1)
    
    print(f"发现 {len(dir_dict)} 个类别目录")
    for k, v in dir_dict.items():
        print(f"  {k}: {v}")
    
    pcap2npy_custom(dir_path_dict=dir_dict, save_path_dict=dir_dict)
