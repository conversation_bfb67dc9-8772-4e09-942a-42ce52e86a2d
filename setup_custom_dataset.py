#!/usr/bin/env python3
"""
自定义数据集设置脚本
用于自动检测数据集文件夹并更新配置文件
"""

import os
import sys

def get_dataset_folders(dataset_path):
    """
    获取数据集文件夹列表
    """
    if not os.path.exists(dataset_path):
        print(f"错误: 数据集路径不存在: {dataset_path}")
        return None
    
    folders = []
    for item in os.listdir(dataset_path):
        item_path = os.path.join(dataset_path, item)
        if os.path.isdir(item_path):
            folders.append(item)
    
    folders.sort()  # 按字母顺序排序
    return folders

def update_config_file(folders, dataset_path):
    """
    更新config.py文件中的DIR_PATH_DICT
    """
    config_content = []
    
    # 读取现有配置文件
    with open('config.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 查找CustomDatasetConfig类中的DIR_PATH_DICT部分
    in_custom_config = False
    in_dir_path_dict = False
    dict_start_line = -1
    dict_end_line = -1
    
    for i, line in enumerate(lines):
        if 'class CustomDatasetConfig(Config):' in line:
            in_custom_config = True
        elif in_custom_config and line.strip().startswith('class ') and 'CustomDatasetConfig' not in line:
            in_custom_config = False
        elif in_custom_config and 'DIR_PATH_DICT = {' in line:
            in_dir_path_dict = True
            dict_start_line = i
        elif in_dir_path_dict and line.strip() == '}':
            dict_end_line = i
            break
    
    if dict_start_line == -1 or dict_end_line == -1:
        print("错误: 无法找到CustomDatasetConfig中的DIR_PATH_DICT")
        return False
    
    # 生成新的DIR_PATH_DICT
    new_dict_lines = []
    new_dict_lines.append("    DIR_PATH_DICT = {")
    
    for i, folder in enumerate(folders):
        folder_path = os.path.join(dataset_path, folder).replace('\\', '/')
        line = f"                     {i}: r'{folder_path}'"
        if i < len(folders) - 1:
            line += ","
        new_dict_lines.append(line)
    
    new_dict_lines.append("                     }")
    
    # 替换配置文件内容
    new_lines = lines[:dict_start_line] + [line + '\n' for line in new_dict_lines] + lines[dict_end_line+1:]
    
    # 同时更新NUM_CLASSES
    for i, line in enumerate(new_lines):
        if 'class CustomDatasetConfig(Config):' in line:
            # 查找NUM_CLASSES行
            for j in range(i, min(i+50, len(new_lines))):
                if 'NUM_CLASSES = ' in new_lines[j]:
                    new_lines[j] = f"    NUM_CLASSES = {len(folders)}\n"
                    break
            break
    
    # 写回文件
    with open('config.py', 'w', encoding='utf-8') as f:
        f.writelines(new_lines)
    
    return True

def main():
    dataset_path = "/home/<USER>/NetMamba/dataset/database_clean"
    
    print("=== 自定义数据集设置脚本 ===")
    print(f"数据集路径: {dataset_path}")
    
    # 获取文件夹列表
    folders = get_dataset_folders(dataset_path)
    if folders is None:
        return
    
    print(f"\n发现 {len(folders)} 个类别文件夹:")
    for i, folder in enumerate(folders):
        print(f"  {i}: {folder}")
    
    # 确认
    response = input(f"\n是否使用这 {len(folders)} 个文件夹更新配置? (y/n): ")
    if response.lower() != 'y':
        print("操作已取消")
        return
    
    # 更新配置文件
    if update_config_file(folders, dataset_path):
        print("\n✅ 配置文件已成功更新!")
        print(f"   - NUM_CLASSES 设置为: {len(folders)}")
        print(f"   - DIR_PATH_DICT 已更新为实际的文件夹路径")
        
        print("\n下一步操作:")
        print("1. 确保您的数据文件格式正确 (.pcap 或 .npz)")
        print("2. 运行数据预处理:")
        print("   python pcap2npy.py --dataset custom")
        print("   python preprocess.py --dataset custom")
        print("3. 开始训练:")
        print("   python train_new.py --dataset custom --point 15")
        
    else:
        print("❌ 配置文件更新失败")

if __name__ == "__main__":
    main()
