import os

'''
Training Configuration
'''
class Config:
    BATCH_SIZE = 102
    GRADIENT_ACCUMULATION = 5
    MAX_EPOCH = 20
    LR = 1e-2
    LR_MIN = 1e-4
    LABEL_SMOOTHING = 0
    WEIGHT_DECAY = 0
    WARM_UP = 0.1
    SEED = 32
    DROPOUT = 0.2
    DOWNSTREAM_DROPOUT = 0.0

    EMBEDDING_SIZE = 64
    H_FEATS = 128
    NUM_CLASSES = 14

    PMI_WINDOW_SIZE = 5
    PAD_TRUNC_DIGIT = 256
    FLOW_PAD_TRUNC_LENGTH = 50
    BYTE_PAD_TRUNC_LENGTH = 150
    HEADER_BYTE_PAD_TRUNC_LENGTH = 50
    ANOMALOUS_FLOW_THRESHOLD = 10000


'''
ISCX-VPN Dataset Configuration
'''
class ISCXVPNConfig(Config):
    TRAIN_DATA = r'/data1/zhz/ISCX-VPN-NonVPN-2016/VPN_SPLIT/TCP/train.npz'
    HEADER_TRAIN_DATA = r'/data1/zhz/ISCX-VPN-NonVPN-2016/VPN_SPLIT/TCP/header_train.npz'
    TEST_DATA = r'/data1/zhz/ISCX-VPN-NonVPN-2016/VPN_SPLIT/TCP/test.npz'
    HEADER_TEST_DATA = r'/data1/zhz/ISCX-VPN-NonVPN-2016/VPN_SPLIT/TCP/header_test.npz'

    TRAIN_GRAPH_DATA = r'/data1/zhz/ISCX-VPN-NonVPN-2016/VPN_SPLIT/TCP/train_graph.dgl'
    HEADER_TRAIN_GRAPH_DATA = r'/data1/zhz/ISCX-VPN-NonVPN-2016/VPN_SPLIT/TCP/header_train_graph.dgl'
    TEST_GRAPH_DATA = r'/data1/zhz/ISCX-VPN-NonVPN-2016/VPN_SPLIT/TCP/test_graph.dgl'
    HEADER_TEST_GRAPH_DATA = r'/data1/zhz/ISCX-VPN-NonVPN-2016/VPN_SPLIT/TCP/header_test_graph.dgl'

    MIX_MODEL_CHECKPOINT = r'./checkpoints/mix_model_iscx_vpn.pth'

    NUM_CLASSES = 6
    MAX_SEG_PER_CLASS = 9999
    NUM_WORKERS = 5

    BATCH_SIZE = 16
    GRADIENT_ACCUMULATION = 1
    MAX_EPOCH = 20
    LR = 1e-2
    LR_MIN = 1e-4
    LABEL_SMOOTHING = 0
    WEIGHT_DECAY = 0
    WARM_UP = 0.1
    SEED = 32
    DROPOUT = 0.0
    DOWNSTREAM_DROPOUT = 0.0
    EMBEDDING_SIZE = 64
    H_FEATS = 128

    DIR_PATH_DICT = {0: r'/data1/zhz/ISCX-VPN-NonVPN-2016/VPN_SPLIT/TCP/Chat',
                     1: r'/data1/zhz/ISCX-VPN-NonVPN-2016/VPN_SPLIT/TCP/Email',
                     2: r'/data1/zhz/ISCX-VPN-NonVPN-2016/VPN_SPLIT/TCP/File',
                     3: r'/data1/zhz/ISCX-VPN-NonVPN-2016/VPN_SPLIT/TCP/P2P',
                     4: r'/data1/zhz/ISCX-VPN-NonVPN-2016/VPN_SPLIT/TCP/Streaming',
                     5: r'/data1/zhz/ISCX-VPN-NonVPN-2016/VPN_SPLIT/TCP/VoIP',
                     }


'''
ISCX-NonVPN Dataset Configuration
'''
class ISCXNonVPNConfig(Config):
    TRAIN_DATA = r'/data1/zhz/ISCX-VPN-NonVPN-2016/NonVPN_SPLIT/TCP/train.npz'
    HEADER_TRAIN_DATA = r'/data1/zhz/ISCX-VPN-NonVPN-2016/NonVPN_SPLIT/TCP/header_train.npz'
    TEST_DATA = r'/data1/zhz/ISCX-VPN-NonVPN-2016/NonVPN_SPLIT/TCP/test.npz'
    HEADER_TEST_DATA = r'/data1/zhz/ISCX-VPN-NonVPN-2016/NonVPN_SPLIT/TCP/header_test.npz'

    TRAIN_GRAPH_DATA = r'/data1/zhz/ISCX-VPN-NonVPN-2016/NonVPN_SPLIT/TCP/train_graph.dgl'
    HEADER_TRAIN_GRAPH_DATA = r'/data1/zhz/ISCX-VPN-NonVPN-2016/NonVPN_SPLIT/TCP/header_train_graph.dgl'
    TEST_GRAPH_DATA = r'/data1/zhz/ISCX-VPN-NonVPN-2016/NonVPN_SPLIT/TCP/test_graph.dgl'
    HEADER_TEST_GRAPH_DATA = r'/data1/zhz/ISCX-VPN-NonVPN-2016/NonVPN_SPLIT/TCP/header_test_graph.dgl'

    MIX_MODEL_CHECKPOINT = r'./checkpoints/mix_model_iscx_nonvpn.pth'

    NUM_CLASSES = 6
    MAX_SEG_PER_CLASS = 9999
    NUM_WORKERS = 5

    BATCH_SIZE = 102
    GRADIENT_ACCUMULATION = 5
    MAX_EPOCH = 120
    LR = 1e-2
    LR_MIN = 1e-5
    LABEL_SMOOTHING = 0.01
    WEIGHT_DECAY = 0
    WARM_UP = 0.1
    SEED = 32
    DROPOUT = 0.1
    DOWNSTREAM_DROPOUT = 0.15
    EMBEDDING_SIZE = 64
    H_FEATS = 128

    DIR_PATH_DICT = {0: r'/data1/zhz/ISCX-VPN-NonVPN-2016/NonVPN_SPLIT/TCP/Chat',
                     1: r'/data1/zhz/ISCX-VPN-NonVPN-2016/NonVPN_SPLIT/TCP/Email',
                     2: r'/data1/zhz/ISCX-VPN-NonVPN-2016/NonVPN_SPLIT/TCP/File',
                     3: r'/data1/zhz/ISCX-VPN-NonVPN-2016/NonVPN_SPLIT/TCP/Streaming',
                     4: r'/data1/zhz/ISCX-VPN-NonVPN-2016/NonVPN_SPLIT/TCP/Video',
                     5: r'/data1/zhz/ISCX-VPN-NonVPN-2016/NonVPN_SPLIT/TCP/VoIP',
                     }


'''
ISCX-Tor Dataset Configuration
'''
class ISCXTorConfig(Config):
    TRAIN_DATA = r'/data1/zhz/ISCX-Tor-NonTor-2017/Tor/Pcaps/TOR_SPLIT/TCP/train.npz'
    HEADER_TRAIN_DATA = r'/data1/zhz/ISCX-Tor-NonTor-2017/Tor/Pcaps/TOR_SPLIT/TCP/header_train.npz'
    TEST_DATA = r'/data1/zhz/ISCX-Tor-NonTor-2017/Tor/Pcaps/TOR_SPLIT/TCP/test.npz'
    HEADER_TEST_DATA = r'/data1/zhz/ISCX-Tor-NonTor-2017/Tor/Pcaps/TOR_SPLIT/TCP/header_test.npz'

    TRAIN_GRAPH_DATA = r'/data1/zhz/ISCX-Tor-NonTor-2017/Tor/Pcaps/TOR_SPLIT/TCP/train_graph.dgl'
    HEADER_TRAIN_GRAPH_DATA = r'/data1/zhz/ISCX-Tor-NonTor-2017/Tor/Pcaps/TOR_SPLIT/TCP/header_train_graph.dgl'
    TEST_GRAPH_DATA = r'/data1/zhz/ISCX-Tor-NonTor-2017/Tor/Pcaps/TOR_SPLIT/TCP/test_graph.dgl'
    HEADER_TEST_GRAPH_DATA = r'/data1/zhz/ISCX-Tor-NonTor-2017/Tor/Pcaps/TOR_SPLIT/TCP/header_test_graph.dgl'

    MIX_MODEL_CHECKPOINT = r'./checkpoints/mix_model_iscx_tor.pth'

    NUM_CLASSES = 8
    MAX_SEG_PER_CLASS = 9999
    NUM_WORKERS = 5

    BATCH_SIZE = 32
    GRADIENT_ACCUMULATION = 1
    MAX_EPOCH = 100
    LR = 1e-2
    LR_MIN = 1e-4
    LABEL_SMOOTHING = 0
    WEIGHT_DECAY = 0
    WARM_UP = 0.1
    SEED = 32
    DROPOUT = 0.0
    DOWNSTREAM_DROPOUT = 0.0
    EMBEDDING_SIZE = 64
    H_FEATS = 128

    DIR_PATH_DICT = {0: r'/data1/zhz/ISCX-Tor-NonTor-2017/Tor/Pcaps/TOR_SPLIT/TCP/Audio-Streaming',
                     1: r'/data1/zhz/ISCX-Tor-NonTor-2017/Tor/Pcaps/TOR_SPLIT/TCP/Browsing',
                     2: r'/data1/zhz/ISCX-Tor-NonTor-2017/Tor/Pcaps/TOR_SPLIT/TCP/Chat',
                     3: r'/data1/zhz/ISCX-Tor-NonTor-2017/Tor/Pcaps/TOR_SPLIT/TCP/File',
                     4: r'/data1/zhz/ISCX-Tor-NonTor-2017/Tor/Pcaps/TOR_SPLIT/TCP/Mail',
                     5: r'/data1/zhz/ISCX-Tor-NonTor-2017/Tor/Pcaps/TOR_SPLIT/TCP/P2P',
                     6: r'/data1/zhz/ISCX-Tor-NonTor-2017/Tor/Pcaps/TOR_SPLIT/TCP/Video-Streaming',
                     7: r'/data1/zhz/ISCX-Tor-NonTor-2017/Tor/Pcaps/TOR_SPLIT/TCP/VoIP'
                     }


'''
ISCX-NonTor Dataset Configuration
'''
class ISCXNonTorConfig(Config):
    TRAIN_DATA = r'/data1/zhz/ISCX-Tor-NonTor-2017/Tor/Pcaps/NonTOR_SPLIT/TCP/train.npz'
    HEADER_TRAIN_DATA = r'/data1/zhz/ISCX-Tor-NonTor-2017/Tor/Pcaps/NonTOR_SPLIT/TCP/header_train.npz'
    TEST_DATA = r'/data1/zhz/ISCX-Tor-NonTor-2017/Tor/Pcaps/NonTOR_SPLIT/TCP/test.npz'
    HEADER_TEST_DATA = r'/data1/zhz/ISCX-Tor-NonTor-2017/Tor/Pcaps/NonTOR_SPLIT/TCP/header_test.npz'

    TRAIN_GRAPH_DATA = r'/data1/zhz/ISCX-Tor-NonTor-2017/Tor/Pcaps/NonTOR_SPLIT/TCP/train_graph.dgl'
    HEADER_TRAIN_GRAPH_DATA = r'/data1/zhz/ISCX-Tor-NonTor-2017/Tor/Pcaps/NonTOR_SPLIT/TCP/header_train_graph.dgl'
    TEST_GRAPH_DATA = r'/data1/zhz/ISCX-Tor-NonTor-2017/Tor/Pcaps/NonTOR_SPLIT/TCP/test_graph.dgl'
    HEADER_TEST_GRAPH_DATA = r'/data1/zhz/ISCX-Tor-NonTor-2017/Tor/Pcaps/NonTOR_SPLIT/TCP/header_test_graph.dgl'

    MIX_MODEL_CHECKPOINT = r'./checkpoints/mix_model_iscx_nontor.pth'

    NUM_CLASSES = 8
    MAX_SEG_PER_CLASS = 9999
    NUM_WORKERS = 5

    BATCH_SIZE = 102
    GRADIENT_ACCUMULATION = 5
    MAX_EPOCH = 120
    LR = 1e-2
    LR_MIN = 1e-4
    LABEL_SMOOTHING = 0
    WEIGHT_DECAY = 0
    WARM_UP = 0.1
    SEED = 32
    DROPOUT = 0.2
    DOWNSTREAM_DROPOUT = 0.1
    EMBEDDING_SIZE = 64
    H_FEATS = 128

    DIR_PATH_DICT = {0: r'/data1/zhz/ISCX-Tor-NonTor-2017/Tor/Pcaps/NonTOR_SPLIT/TCP/Audio',
                     1: r'/data1/zhz/ISCX-Tor-NonTor-2017/Tor/Pcaps/NonTOR_SPLIT/TCP/Browsing',
                     2: r'/data1/zhz/ISCX-Tor-NonTor-2017/Tor/Pcaps/NonTOR_SPLIT/TCP/Chat',
                     3: r'/data1/zhz/ISCX-Tor-NonTor-2017/Tor/Pcaps/NonTOR_SPLIT/TCP/Email',
                     4: r'/data1/zhz/ISCX-Tor-NonTor-2017/Tor/Pcaps/NonTOR_SPLIT/TCP/FTP',
                     5: r'/data1/zhz/ISCX-Tor-NonTor-2017/Tor/Pcaps/NonTOR_SPLIT/TCP/P2P',
                     6: r'/data1/zhz/ISCX-Tor-NonTor-2017/Tor/Pcaps/NonTOR_SPLIT/TCP/Video',
                     7: r'/data1/zhz/ISCX-Tor-NonTor-2017/Tor/Pcaps/NonTOR_SPLIT/TCP/VoIP',
                     }


'''
Custom Dataset Configuration for /home/<USER>/NetMamba/dataset/database_clean
'''
class CustomDatasetConfig(Config):
    # 数据文件路径 - 根据您的需求调整
    TRAIN_DATA = r'/home/<USER>/NetMamba/dataset/processed/train.npz'
    HEADER_TRAIN_DATA = r'/home/<USER>/NetMamba/dataset/processed/header_train.npz'
    TEST_DATA = r'/home/<USER>/NetMamba/dataset/processed/test.npz'
    HEADER_TEST_DATA = r'/home/<USER>/NetMamba/dataset/processed/header_test.npz'

    TRAIN_GRAPH_DATA = r'/home/<USER>/NetMamba/dataset/processed/train_graph.dgl'
    HEADER_TRAIN_GRAPH_DATA = r'/home/<USER>/NetMamba/dataset/processed/header_train_graph.dgl'
    TEST_GRAPH_DATA = r'/home/<USER>/NetMamba/dataset/processed/test_graph.dgl'
    HEADER_TEST_GRAPH_DATA = r'/home/<USER>/NetMamba/dataset/processed/header_test_graph.dgl'

    MIX_MODEL_CHECKPOINT = r'./checkpoints/mix_model_custom.pth'

    # 13个类别
    NUM_CLASSES = 13
    MAX_SEG_PER_CLASS = 9999
    NUM_WORKERS = 5

    BATCH_SIZE = 32
    GRADIENT_ACCUMULATION = 1
    MAX_EPOCH = 100
    LR = 1e-2
    LR_MIN = 1e-4
    LABEL_SMOOTHING = 0
    WEIGHT_DECAY = 0
    WARM_UP = 0.1
    SEED = 32
    DROPOUT = 0.1
    DOWNSTREAM_DROPOUT = 0.1
    EMBEDDING_SIZE = 64
    H_FEATS = 128
    NUM_CLASSES = 14
    DIR_PATH_DICT = {0: r'/home/<USER>/NetMamba/dataset/database_clean/DCS',
                     1: r'/home/<USER>/NetMamba/dataset/database_clean/Distance',
                     2: r'/home/<USER>/NetMamba/dataset/database_clean/Flame_Sensor',
                     3: r'/home/<USER>/NetMamba/dataset/database_clean/Heart_Rate',
                     4: r'/home/<USER>/NetMamba/dataset/database_clean/IR_Receiver',
                     5: r'/home/<USER>/NetMamba/dataset/database_clean/PLC',
                     6: r'/home/<USER>/NetMamba/dataset/database_clean/POWER',
                     7: r'/home/<USER>/NetMamba/dataset/database_clean/Soil_Moisture',
                     8: r'/home/<USER>/NetMamba/dataset/database_clean/Sound_Sensor',
                     9: r'/home/<USER>/NetMamba/dataset/database_clean/Temperature_and_Humidity',
                     10: r'/home/<USER>/NetMamba/dataset/database_clean/Water_Level',
                     11: r'/home/<USER>/NetMamba/dataset/database_clean/abnormal',
                     12: r'/home/<USER>/NetMamba/dataset/database_clean/camera',
                     13: r'/home/<USER>/NetMamba/dataset/database_clean/phValue',
                     }
    # 根据您的实际数据集结构自动生成路径字典
    # 这将在运行时动态获取文件夹列表
    @property
    def DIR_PATH_DICT(self):
        dataset_root = '/home/<USER>/NetMamba/dataset/database_clean'
        if not hasattr(self, '_dir_path_dict'):
            folders = []
            if os.path.exists(dataset_root):
                for item in os.listdir(dataset_root):
                    item_path = os.path.join(dataset_root, item)
                    if os.path.isdir(item_path):
                        folders.append(item)
                folders.sort()  # 按字母顺序排序

            self._dir_path_dict = {}
            for i, folder in enumerate(folders):
                self._dir_path_dict[i] = os.path.join(dataset_root, folder)

            # 更新类别数量
            self.NUM_CLASSES = len(folders)

        return self._dir_path_dict


if __name__ == '__main__':
    config = Config()
